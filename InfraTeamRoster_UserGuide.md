# Infra Team Roster Management System - User Guide

## 📋 Overview
This Excel-based roster management system automates weekly scheduling for the Infrastructure team, handles vacation tracking, and ensures proper shift coverage with role-based assignments.

## 🚀 Initial Setup

### Step 1: Download and Setup
1. Save the Excel file as `InfraTeamRoster.xlsm` (macro-enabled format)
2. Open the file in Microsoft Excel
3. **Enable Macros** when prompted (Essential for functionality)
4. The system will automatically initialize with default team data

### Step 2: First-Time Configuration
1. The workbook will auto-create 4 main sheets:
   - **Dashboard** - Main control center
   - **Team_Database** - Employee information
   - **Vacation_Tracker** - Leave management
   - **Generated Rosters** - Weekly schedules (created as needed)

## 📊 Using the Dashboard

### Main Features
- **Team Overview Statistics** - Shows total members, available staff, vacation count
- **Quick Action Buttons** - One-click roster generation
- **Current Week Information** - Today's date and current week range
- **Usage Instructions** - Built-in help

### Action Buttons
| Button | Function |
|--------|----------|
| **Generate This Week** | Creates roster for current week |
| **Generate Next Week** | Creates roster for next week |
| **Manage Vacations** | Opens vacation tracker |
| **Team Database** | Opens employee database |

## 👥 Team Database Management

### Step 3: Update Team Information
1. Click **"Team Database"** button on Dashboard
2. Update employee details in the table:
   - Employee ID
   - Full Name
   - Role (Team Lead, Senior Engineer, Junior Engineer, Intern)
   - Shift Preference (Day, Evening, Night)
   - Contact Information
   - Start Date
   - Status (Active/Inactive)

### Adding New Team Members
1. Go to **Team_Database** sheet
2. Add new row in the table
3. Fill in all required fields
4. Set Status to "Active"

### Removing Team Members
1. Change Status from "Active" to "Inactive"
2. Do NOT delete rows (maintains historical data)

## 🏖️ Vacation Management

### Step 4: Managing Vacation Requests
1. Click **"Manage Vacations"** button on Dashboard
2. Add vacation entries with:
   - **Employee Name** (must match Team Database exactly)
   - **Start Date** and **End Date**
   - **Status** (Pending, Approved, Denied)
   - **Type** (Personal, Sick Leave, Holiday)
   - **Notes** (optional details)

### Vacation Status Options
- **Pending** - Request submitted, awaiting approval
- **Approved** - Vacation confirmed, affects roster generation
- **Denied** - Request rejected, no impact on scheduling

## 📅 Generating Weekly Rosters

### Step 5: Create Weekly Schedules

#### For Current Week:
1. Go to **Dashboard**
2. Click **"Generate This Week"**
3. New roster sheet created automatically
4. Review assignments and make manual adjustments if needed

#### For Next Week:
1. Click **"Generate Next Week"**
2. System generates roster for following Monday-Sunday
3. Useful for advance planning

### Roster Features
- **Smart Shift Assignment** - Considers role levels and preferences
- **Vacation Integration** - Automatically excludes staff on approved leave
- **Weekend Coverage** - Reduced staffing with senior oversight
- **On-Call Rotation** - Primary and backup assignments
- **Conflict Detection** - Highlights scheduling issues

## 🔧 Advanced Features

### Shift Assignment Logic
- **Morning Shift (8AM-4PM)** - Balanced rotation across all roles
- **Evening Shift (4PM-12AM)** - Preference-based assignments
- **Night Shift (12AM-8AM)** - Senior staff prioritized
- **On-Call Primary** - Rotating senior coverage
- **On-Call Backup** - Secondary support rotation

### Automatic Conflict Resolution
- Skips employees on approved vacation
- Ensures senior coverage for critical shifts
- Avoids double-booking individuals
- Provides backup assignments

## 📋 Weekly Workflow

### Step 6: Recommended Weekly Process
1. **Monday Morning**:
   - Update any new vacation requests
   - Generate current week roster if needed
   - Review and distribute to team

2. **Wednesday**:
   - Generate next week's roster
   - Send advance notice to team
   - Allow time for any adjustment requests

3. **Friday**:
   - Finalize next week's assignments
   - Update vacation tracker with any changes
   - Prepare emergency contact list

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### Macros Not Working
- **Problem**: Buttons don't respond
- **Solution**: Enable macros in Excel security settings
- **Steps**: File → Options → Trust Center → Macro Settings → Enable all macros

#### Vacation Not Reflecting in Roster
- **Problem**: Employee shows in roster despite approved vacation
- **Solution**: Check exact name spelling in both sheets
- **Requirement**: Names must match exactly between Team_Database and Vacation_Tracker

#### Missing Team Members in Assignments
- **Problem**: Some employees never appear in rosters
- **Solution**: Verify Status is set to "Active" in Team_Database

#### Roster Generation Errors
- **Problem**: Error messages during roster creation
- **Solution**: 
  1. Check for empty rows in Team_Database
  2. Ensure all required fields are filled
  3. Verify date formats in Vacation_Tracker

## 📞 Emergency Procedures

### Step 7: Handling Urgent Changes
1. **Last-Minute Sick Leave**:
   - Add entry to Vacation_Tracker immediately
   - Regenerate affected roster
   - Contact backup assignments

2. **Emergency Coverage**:
   - Check "On-Call Backup" assignments
   - Use emergency contact list in roster summary
   - Document changes for next week planning

## 🔄 Maintenance Tasks

### Monthly Maintenance
- [ ] Archive old roster sheets
- [ ] Update team database with any role changes
- [ ] Review vacation balance tracking
- [ ] Clean up completed vacation entries

### Quarterly Review
- [ ] Analyze shift distribution fairness
- [ ] Update emergency contact information
- [ ] Review and adjust rotation patterns
- [ ] Backup the entire workbook

## 📈 Customization Options

### Modifying Shift Times
1. Open VBA Editor (Alt + F11)
2. Find shift time references in code
3. Update time ranges as needed
4. Test with sample roster generation

### Adding New Roles
1. Update Team_Database with new role types
2. Modify assignment logic in VBA if needed
3. Test roster generation with new roles

### Holiday Scheduling
1. Add holiday dates to vacation tracker
2. Mark as "Company Holiday" type
3. System will note reduced coverage automatically

## 📊 Reports and Analytics

### Available Reports
- **Weekly Coverage Summary** - Shows staffing levels
- **Vacation Impact Analysis** - Tracks leave effects
- **Shift Distribution Report** - Ensures fair rotation
- **Emergency Contact List** - Quick reference for managers

## 🔐 Data Security

### Best Practices
- Save regular backups of the workbook
- Restrict edit access to authorized personnel only
- Keep personal contact information confidential
- Archive old data according to company policy

## 📞 Support and Contact

### For Technical Issues
- Check this user guide first
- Verify macro settings are enabled
- Test with sample data
- Contact IT support if problems persist

### For Process Questions
- Review team scheduling policies
- Consult with team lead for coverage requirements
- Document any special arrangements needed

---

## 🎯 Quick Reference Card

| Task | Steps |
|------|-------|
| **Generate Current Week** | Dashboard → "Generate This Week" |
| **Add Vacation** | Dashboard → "Manage Vacations" → Add row |
| **Update Team Info** | Dashboard → "Team Database" → Edit table |
| **View Roster** | Look for "Roster_MMDD_YYYY" sheet tabs |
| **Emergency Contact** | Check bottom of any generated roster |

---

*Last Updated: [Current Date]*
*Version: 1.0*
*Created for: Infrastructure Team Roster Management*